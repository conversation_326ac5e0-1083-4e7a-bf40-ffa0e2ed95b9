import md5 from 'md5'
import moment from 'moment'

export function md5Hash(input) {
  return md5(input).toString()
}
export function isValidPhoneNumber(phoneNumber) {
  const regex = /^1[3-9]\d{9}$/

  return regex.test(phoneNumber)
}

export function isValidEmail(email) {
  const regex = /^[\w.%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i

  return regex.test(email)
}
export function formatTime(time) {
  return moment(time).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 将秒数格式化为“天小时分秒”的字符串
 * - 负数或非法输入将按 0 处理
 * - 会省略前导为 0 的单位（例如：59 -> 59秒，61 -> 1分1秒，3600 -> 1小时0分0秒）
 */
export function formatSecondsToDHMS(input) {
  const total = Math.max(0, Math.floor(Number(input) || 0))

  const days = Math.floor(total / 86400)
  const hours = Math.floor((total % 86400) / 3600)
  const minutes = Math.floor((total % 3600) / 60)
  const seconds = total % 60

  const parts = []

  if (days)
    parts.push(`${days}天`)

  // 若有天，则展示小时，即使为 0；无天时仅在非 0 时展示
  if (hours || parts.length)
    parts.push(`${hours}小时`)
  if (minutes || parts.length)
    parts.push(`${minutes}分`)
  parts.push(`${seconds}秒`)

  return parts.join('')
}
