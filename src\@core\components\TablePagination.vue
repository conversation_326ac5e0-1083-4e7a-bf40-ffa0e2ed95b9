<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  page: number
  itemsPerPage: number
  totalItems: number
  showMeta?: boolean
  hideWhenSinglePage?: boolean // 新增属性：当只有一页时是否隐藏分页
}

interface Emit {
  (e: 'update:page', value: number): void
}

const props = defineProps<Props>()

const emit = defineEmits<Emit>()

const updatePage = (value: number) => {
  emit('update:page', value)
}

// 计算分页长度，最小为1
const paginationLength = computed(() => {
  const totalPages = Math.ceil(props.totalItems / props.itemsPerPage)

  return Math.max(totalPages, 1) // 最小显示1页
})
</script>

<template>
  <div>
    <VDivider />

    <div class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 px-6 py-3">
      <p
        v-if="showMeta"
        class="text-disabled mb-0"
      >
        {{ paginationMeta({ page, itemsPerPage }, totalItems) }}
      </p>

      <VPagination
        :model-value="page"
        active-color="primary"
        :length="paginationLength"
        :total-visible="$vuetify.display.xs ? 1 : Math.min(paginationLength, 5)"
        @update:model-value="updatePage"
      />
    </div>
  </div>
</template>
