<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'
import TablePagination from '@/@core/components/TablePagination.vue'
import { $get, $post } from '@/utils/api'

// AP分组数据接口
interface ApGroup {
  id: string
  name: string
  remark: string
  utime: string
  apCount: number
  userId: string
}

// AP设备接口
interface ApDevice {
  id: string
  sn: string
  mac: string
  model: string
  name: string
  online: boolean
  dType: number // 0 AP 1 AC
  userId: string
  bindTime: string
  status: number // 1 已绑定
  deviceInfo: object
  selected: boolean // 用于UI选择状态
  runStatus?: number // 运行状态：0-未知，1-成功，2-失败
  resultData?: string // 命令执行结果数据
  result?: boolean // 操作结果：true-成功，false-失败
  data?: string // 操作返回的数据
}

// 响应式数据
const loading = ref(false)
const isAddDrawerOpen = ref(false)
const isEditDrawerOpen = ref(false)
const currentEditGroup = ref<ApGroup | null>(null)
const isEditMode = ref(false)
const isApListDrawerOpen = ref(false)
const currentViewGroup = ref<ApGroup | null>(null)
const type = ref('restart')

// 操作详情抽屉状态
const isRestartDetailDrawerOpen = ref(false)
const isResetDetailDrawerOpen = ref(false)
const isUpdateDetailDrawerOpen = ref(false)
const currentOperationGroup = ref<ApGroup | null>(null)
const operationStatus = ref<'pending' | 'processing' | 'completed' | 'error'>('pending')

// 分页状态 - 参考 device/create 实现
const itemsPerPage = ref(10)
const page = ref(1)
const totalGroups = ref(0)

// 排序相关变量
const sortBy = ref<{ key: string; order?: 'asc' | 'desc' }[]>([])

// 表头配置
const headers = [
  { title: '分组名称', key: 'name', sortable: true },
  { title: '最后修改时间', key: 'utime', sortable: true },
  { title: 'AP数量', key: 'apCount', sortable: false },
  { title: '备注', key: 'remark', sortable: true },
  { title: '操作', key: 'actions', sortable: false },
]

// 分组数据
const groupData = ref({
  groups: [] as ApGroup[],
})

// 新建表单数据
const addFormData = ref({
  name: '',
  remark: '',
  deviceSns: [] as string[],
})

// 编辑表单数据
const editFormData = ref({
  name: '',
  remark: '',
  deviceSns: [] as string[],
})

// AP设备数据
const apData = ref({
  total: 0,
  apList: [] as ApDevice[],
})

// 本地分页相关
const apPage = ref(1)
const apItemsPerPage = ref(10)
const selectedApRows = ref<string[]>([])

// 获取AP列表
const getApList = (params: any) => {
  return $get('/v1/apGroupDevice', params).then(res => {
    if (res.msg === 'success') {
      apData.value.total = res.result.count || 0

      // 为每个AP设备添加selected字段
      apData.value.apList = (res.result || [])
        .filter((item: any) => item.sn)
        .map((item: any) => ({
          ...item,
          selected: item.groupId != null, // 默认未选中
        })) || []
    }
    else {
      console.error('获取AP列表失败:', res.msg)
    }
  })
}

const resultList: any = ref([])

const getAPStatus = (bulkId: string, groupID: string) => {
  setTimeout(() => {
    $get(`/v1/bulkCmdResult/${bulkId}`, {}).then(res => {
      if (!res.result || !Array.isArray(res.result.rows))
        return
      const statusList = res.result.rows

      console.log(statusList, '命令结果')
      getApList({
        groupId: groupID,
        withUnBind: 0,
      }).then(() => {
      // 根据 sn 匹配更新 apData.value.apList 中的设备状态
        const list = apData.value.apList
        for (const statusItem of statusList) {
          const sn = statusItem.sn

          // 在 apData.value.apList 中查找匹配的 AP 设备
          const apIndex = list.findIndex((ap: any) => ap.sn === sn)

          if (apIndex !== -1)
            list[apIndex] = Object.assign(list[apIndex], statusItem)

          else
            console.warn(`未找到 SN 为 ${sn} 的设备`)
        }
        if (type.value === 'restart')
          openRestartDetail()

        if (type.value === 'reset')
          openResetDetail()

        if (type.value === 'update')
          openUpdateDetail()

        resultList.value = list

        // 打印更新后的结果
        console.log('更新后的 AP 列表:', list)
        console.log(`分组 ${groupID} 的设备状态更新完成`)
      })
    }).catch(error => {
      console.error('获取设备状态失败:', error)
      ElMessage.error('获取设备状态失败')
    })
  }, 3000)
}

// 更新选中的AP行
const updateSelectedApRows = () => {
  selectedApRows.value = apData.value.apList
    .filter(ap => ap.selected)
    .map(ap => ap.sn)
}

// 本地分页的AP列表
const availableAps = computed(() => {
  const start = (apPage.value - 1) * apItemsPerPage.value
  const end = start + apItemsPerPage.value

  return apData.value.apList.slice(start, end)
})

// 监听AP分页变化
watch([apPage, apItemsPerPage], () => {
  // 本地分页不需要重新请求数据
})

// 监听AP选择状态变化
watch(() => apData.value.apList, () => {
  updateSelectedApRows()
}, { deep: true })

// 全选/取消全选AP
const toggleSelectAllAps = (selected: boolean) => {
  availableAps.value.forEach(ap => {
    ap.selected = selected
  })

  // 更新选中列表
  updateSelectedApRows()
}

// 获取分组列表API
const getGroupList = () => {
  loading.value = true

  $get('/v1/apGroup', {
    page: page.value,
    size: itemsPerPage.value,
  }).then(res => {
    if (res.msg === 'success') {
      groupData.value.groups = res.result.rows || []
      totalGroups.value = res.result.count || 0
    }
    else {
      console.error('获取分组列表失败:', res.msg)
    }
  }).finally(() => {
    loading.value = false
  })
}

// 排序事件 - 参考 device/create 实现
const sortchange = (val: any) => {
  sortBy.value = val
}

// 打开新建分组抽屉
const openAddDrawer = () => {
  getApList({
    groupId: '',
    withUnBind: 1,
  }).then(() => {
    isAddDrawerOpen.value = true
  })
}

// 关闭新建分组抽屉
const closeAddDrawer = () => {
  isAddDrawerOpen.value = false

  // 重置表单数据
  addFormData.value = {
    name: '',
    remark: '',
    deviceSns: [],
  }

  // 重置AP选择状态和分页
  apData.value.apList.forEach(ap => {
    ap.selected = false
  })
  apPage.value = 1
}

// 保存新建分组
const saveAddGroup = () => {
  console.log('保存新建分组:', addFormData.value)
  console.log('选中的AP:', apData.value.apList.filter(ap => ap.selected))

  // 获取选中的设备SN列表
  const selectedDeviceSns = apData.value.apList
    .filter(ap => ap.selected)
    .map(ap => ap.sn)

  addFormData.value.deviceSns = selectedDeviceSns

  console.log(addFormData.value)

  // 调用创建分组接口
  $post('/v1/apGroup', addFormData.value).then(res => {
    if (res.msg === 'success') {
      ElMessage.success('分组创建成功')
      closeAddDrawer()

      // 刷新分组列表
      getGroupList()
    }
    else {
      ElMessage.error(res.msg || '分组创建失败')
    }
  }).catch(error => {
    console.error('创建分组失败:', error)
    ElMessage.error('分组创建失败')
  })
}

// 编辑分组
const editGroup = (group: ApGroup) => {
  isEditMode.value = true
  currentEditGroup.value = group
  editFormData.value = {
    name: group.name,
    remark: group.remark,
    deviceSns: [], // 这里需要根据实际API返回的数据来设置
  }

  getApList({
    groupId: group.id,
    withUnBind: 1,
  }).then(() => {
    isEditDrawerOpen.value = true
  })
}

// 关闭编辑抽屉
const closeEditDrawer = () => {
  isEditDrawerOpen.value = false
  isEditMode.value = false
  currentEditGroup.value = null
}

// 保存编辑
const saveEditGroup = () => {
  if (!currentEditGroup.value) {
    ElMessage.error('未选择要编辑的分组')

    return
  }

  console.log('保存编辑:', editFormData.value)
  console.log('选中的AP:', apData.value.apList.filter(ap => ap.selected))

  // 获取选中的设备SN列表
  const selectedDeviceSns = apData.value.apList
    .filter(ap => ap.selected)
    .map(ap => ap.sn)

  editFormData.value.deviceSns = selectedDeviceSns

  // 调用创建分组接口
  $put(`/v1/apGroup/${currentEditGroup.value.id}`, editFormData.value).then(res => {
    if (res.msg === 'success') {
      ElMessage.success('分组编辑成功')
      closeEditDrawer()

      // 刷新分组列表
      getGroupList()
    }
    else {
      ElMessage.error(res.msg || '分组编辑失败')
    }
  }).catch(error => {
    console.error('编辑分组失败:', error)
    ElMessage.error('分组编辑失败')
  })
}

// 删除分组
const deleteGroup = async (group: ApGroup) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该分组吗？分组内的AP不受影响。',
      '删除分组',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        distinguishCancelAndClose: true,
      },
    )

    // 执行删除操作
    console.log('确认删除分组:', group.name)
    $delete(`/v1/apGroup/${group.id}`, {}).then(res => {
      if (res.msg === 'success') {
        ElMessage.success('分组删除成功')
        getGroupList()
      }
      else {
        ElMessage.error(res.msg || '分组删除失败')
      }
    })
  }
  catch (action) {
    if (action === 'cancel')
      console.log('用户取消删除')
    else if (action === 'close')
      console.log('用户关闭对话框')

    // 用户取消或关闭，不执行任何操作
  }
}

// 获取分组AP数据 - 现在使用API数据
const getGroupAps = (): ApDevice[] => {
  return apData.value.apList
}

// 查看分组详情
const viewGroupDetail = (group: ApGroup) => {
  getApList({
    groupId: group.id,
    withUnBind: 0,
  }).then(() => {
    currentViewGroup.value = group
    isApListDrawerOpen.value = true
  })
}

// 查看AP列表
const viewApList = (group: ApGroup) => {
  viewGroupDetail(group)
}

// 打开重启设备详情
const openRestartDetail = () => {
  isRestartDetailDrawerOpen.value = true
}

// 打开恢复出厂设置详情
const openResetDetail = () => {
  isResetDetailDrawerOpen.value = true
}

// 打开检查并更新详情
const openUpdateDetail = () => {
  isUpdateDetailDrawerOpen.value = true
}

// 关闭操作详情抽屉
const closeOperationDrawers = () => {
  isRestartDetailDrawerOpen.value = false
  isResetDetailDrawerOpen.value = false
  isUpdateDetailDrawerOpen.value = false
  currentOperationGroup.value = null
  operationStatus.value = 'pending'
}

// 重启AP
const restartAp = (group: ApGroup) => {
  ElMessageBox.confirm(
    `分组内${group.apCount}个AP将要重启，是否确定？`,
    '重启AP',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    },
  ).then(() => {
    currentOperationGroup.value = group
    type.value = 'restart'
    console.log(1233)

    // 调用重启AP接口
    $get(`/v1/apGroupDeviceSN/${group.id}`, { }).then(res => {
      if (res.msg === 'success') {
        console.log(res.result)
        $post('/v1/bulkCmd', {
          deviceSns: res.result,
          cmd: 'restart',
        }).then(res => {
          if (res.msg === 'success') {
            getAPStatus(res.result, group.id)
            ElMessage.success('重启AP命令已发送')
          }
        })
      }
    })
  }).catch(() => {
    // 用户取消
  })
}

// 恢复出厂设置
const resetToFactory = (group: ApGroup) => {
  ElMessageBox.confirm(
    `分组内${group.apCount}个AP将要恢复出厂设置，是否确定？`,
    '恢复出厂设置',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    },
  ).then(() => {
    currentOperationGroup.value = group
    type.value = 'reset'

    // 调用重启AP接口
    $get(`/v1/apGroupDeviceSN/${group.id}`, { }).then(res => {
      if (res.msg === 'success') {
        console.log(res.result)
        $post('/v1/bulkCmd', {
          deviceSns: res.result,
          cmd: 'factoryReset ',
        }).then(res => {
          if (res.msg === 'success') {
            getAPStatus(res.result, group.id)
            ElMessage.success('重启AP命令已发送')
          }
        })
      }
    })
  }).catch(() => {
    // 用户取消
  })
}

// 检查并更新
const checkAndUpdate = (group: ApGroup) => {
  ElMessageBox.confirm(
    `分组内${group.apCount}个AP将要检查并更新，是否确定？`,
    '检查并更新',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    },
  ).then(() => {
    currentOperationGroup.value = group
    type.value = 'update'

    // 调用检查并更新接口
    $get(`/v1/apGroupDeviceSN/${group.id}`, { }).then(res => {
      if (res.msg === 'success') {
        console.log(res.result)
        $post('/v1/bulkCmd', {
          deviceSns: res.result,
          cmd: 'upgrade ',
        }).then(res => {
          if (res.msg === 'success') {
            getAPStatus(res.result, group.id)
            ElMessage.success('检查并更新命令已发送')
          }
        })
      }
    })
  }).catch(() => {
    // 用户取消
  })
}

// 关闭AP列表抽屉
const closeApListDrawer = () => {
  isApListDrawerOpen.value = false
  currentViewGroup.value = null
}

// 获取当前查看分组的AP列表
const currentGroupAps = computed(() => {
  if (!currentViewGroup.value)
    return []

  return getGroupAps()
})

// 监听分页变化，自动重新获取数据
watch([page, itemsPerPage], () => {
  getGroupList()
})

// 组件挂载时加载数据
onMounted(() => {
  getGroupList()
})

const router = useRouter()

const viewApDetail = (ap: ApDevice) => {
  if (!ap || !ap.id) {
    ElMessage.error('设备ID缺失')

    return
  }
  router.push({
    name: 'network-device-detail',
    query: { id: ap.id },
  })
}
</script>

<template>
  <div class="device-group-page">
    <!-- AP分组列表 -->
    <VCard class="group-table-card">
      <!-- 页面标题和操作按钮 -->
      <div class="d-flex flex-wrap gap-4 ma-6">
        <h2 class="page-title">
          AP分组
        </h2>
        <VSpacer />
        <VBtn
          color="primary"
          @click="openAddDrawer"
        >
          + 新建分组
        </VBtn>
      </div>
      <VDivider />

      <VDataTable
        v-model:items-per-page="itemsPerPage"
        v-model:page="page"
        :headers="headers"
        :items="groupData.groups"
        :loading="loading"
        item-value="id"
        class="text-no-wrap"
        no-data-text="暂无数据"
        :sort-by="sortBy"
        @update:sort-by="sortchange"
      >
        <!-- AP列表列 -->
        <template #item.apCount="{ item }">
          <VBtn
            color="primary"
            variant="text"
            size="small"
            @click="viewApList(item)"
          >
            {{ item.apCount }}
          </VBtn>
        </template>

        <!-- 操作列 -->
        <template #item.actions="{ item }">
          <IconBtn>
            <VIcon icon="tabler-dots-vertical" />
            <VMenu activator="parent">
              <VList>
                <VListItem @click="editGroup(item)">
                  编辑分组
                </VListItem>
                <VListItem @click="viewGroupDetail(item)">
                  查看分组详情
                </VListItem>
                <VListItem @click="restartAp(item)">
                  重启AP
                </VListItem>
                <VListItem @click="resetToFactory(item)">
                  恢复出厂设置
                </VListItem>
                <VListItem @click="checkAndUpdate(item)">
                  检查并更新
                </VListItem>
                <VListItem @click="deleteGroup(item)">
                  删除分组
                </VListItem>
              </VList>
            </VMenu>
          </IconBtn>
        </template>

        <!-- 底部分页 -->
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalGroups"
          />
        </template>
      </VDataTable>
    </VCard>

    <!-- 右侧新建分组抽屉 -->
    <VNavigationDrawer
      v-model="isAddDrawerOpen"
      location="end"
      temporary
      width="600"
      class="add-group-drawer"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            新建分组
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="closeAddDrawer"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间可滚动区域 -->
        <div class="flex-grow-1 overflow-y-scroll">
          <PerfectScrollbar
            :options="{ wheelPropagation: false }"
            class="h-100"
            tag="div"
          >
            <div class="pa-4 d-flex flex-column h-100">
              <!-- 分组名称 -->
              <div class="form-section mb-4">
                <div class="d-flex align-start mb-4">
                  <div class="w-80 h-38 flex-0-0 me-4 text-subtitle-2">
                    分组名称
                  </div>
                  <div class="w-100">
                    <AppTextField
                      v-model="addFormData.name"
                      placeholder="请输入分组名称"
                      append-inner-icon="tabler-edit"
                    />
                  </div>
                </div>
              </div>

              <!-- 备注 -->
              <div class="form-section mb-4">
                <div class="d-flex align-start mb-4">
                  <div class="w-80 h-38 flex-0-0 me-4 text-subtitle-2">
                    备注
                  </div>
                  <div class="w-100">
                    <AppTextField
                      v-model="addFormData.remark"
                      placeholder="请输入备注信息"
                      append-inner-icon="tabler-edit"
                    />
                  </div>
                </div>
              </div>

              <!-- 选择AP -->
              <div class="form-section flex-grow-1">
                <div class="d-flex align-center justify-space-between mb-3">
                  <div class="section-label">
                    AP列表
                  </div>
                  <div class="d-flex align-center">
                    <VBtn
                      size="small"
                      variant="text"
                      @click="toggleSelectAllAps(true)"
                    >
                      全选
                    </VBtn>
                    <VBtn
                      size="small"
                      variant="text"
                      @click="toggleSelectAllAps(false)"
                    >
                      取消全选
                    </VBtn>
                  </div>
                </div>
                <div class="ap-selection-area flex-grow-1">
                  <div
                    v-if="apData.apList.length === 0"
                    class="d-flex align-center justify-center text-medium-emphasis"
                    style="min-block-size: 120px;"
                  >
                    暂无可添加AP
                  </div>
                  <template v-else>
                    <div
                      v-for="ap in availableAps"
                      :key="ap.id"
                      class="ap-item d-flex align-center pa-3 listBorder mb-2"
                    >
                      <VCheckbox
                        v-model="ap.selected"
                        class="me-3"
                        hide-details
                      />
                      <div class="ap-info flex-grow-1">
                        <div class="ap-name">
                          {{ ap.name }}
                          <VChip
                            class="ms-2 mb-2"
                            size="small"
                            color="info"
                          >
                            <span class="font-weight-medium">{{ ap.model }}</span>
                          </VChip>
                        </div>
                        <div class="ap-details text-caption text-disabled">
                          {{ ap.sn }}
                          <span class="ms-2 me-2">/</span>
                          {{ ap.mac }}
                        </div>
                      </div>
                      <VChip
                        :color="ap.online ? 'success' : 'error'"
                        size="small"
                        label
                      >
                        {{ ap.online ? '在线' : '离线' }}
                      </VChip>
                    </div>
                  </template>
                </div>
                <!-- AP分页 -->
                <div
                  v-if="apData.apList.length > 0 && apData.total > apItemsPerPage"
                  class="mt-4"
                >
                  <TablePagination
                    v-model:page="apPage"
                    :items-per-page="apItemsPerPage"
                    :total-items="apData.total"
                  />
                </div>
              </div>
            </div>
          </PerfectScrollbar>
        </div>

        <!-- 底部固定区域 -->
        <VDivider />
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-space-between">
          <div />
          <div>
            <VBtn
              class="me-4"
              color="secondary"
              variant="tonal"
              @click="closeAddDrawer"
            >
              取消
            </VBtn>
            <VBtn
              color="primary"
              @click="saveAddGroup"
            >
              创建
            </VBtn>
          </div>
        </div>
      </div>
    </VNavigationDrawer>

    <!-- 右侧编辑分组抽屉 -->
    <VNavigationDrawer
      v-model="isEditDrawerOpen"
      location="end"
      temporary
      width="600"
      class="edit-group-drawer"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            编辑分组
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="closeEditDrawer"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间可滚动区域 -->
        <div class="flex-grow-1 overflow-y-scroll">
          <PerfectScrollbar
            :options="{ wheelPropagation: false }"
            class="h-100"
            tag="div"
          >
            <div class="pa-4 d-flex flex-column h-100">
              <!-- 分组名称 -->
              <div class="form-section mb-4">
                <div class="d-flex align-start mb-4">
                  <div class="w-80 h-38 flex-0-0 me-4 text-subtitle-2">
                    分组名称
                  </div>
                  <div class="w-100">
                    <AppTextField
                      v-model="editFormData.name"
                      placeholder="请输入分组名称"
                      append-inner-icon="tabler-edit"
                    />
                  </div>
                </div>
              </div>
              <!-- 备注 -->
              <div class="form-section mb-4">
                <div class="d-flex align-start mb-4">
                  <div class="w-80 h-38 flex-0-0 me-4 text-subtitle-2">
                    备注
                  </div>
                  <div class="w-100">
                    <AppTextField
                      v-model="editFormData.remark"
                      placeholder="请输入备注信息"
                      append-inner-icon="tabler-edit"
                    />
                  </div>
                </div>
              </div>

              <!-- 选择AP -->
              <div class="form-section flex-grow-1">
                <div class="d-flex align-center justify-space-between mb-3">
                  <div class="section-label">
                    AP列表
                  </div>
                  <div class="d-flex align-center">
                    <VBtn
                      size="small"
                      variant="text"
                      @click="toggleSelectAllAps(true)"
                    >
                      全选
                    </VBtn>
                    <VBtn
                      size="small"
                      variant="text"
                      @click="toggleSelectAllAps(false)"
                    >
                      取消全选
                    </VBtn>
                  </div>
                </div>
                <div class="ap-selection-area flex-grow-1">
                  <div
                    v-for="ap in availableAps"
                    :key="ap.id"
                    class="ap-item d-flex align-center pa-3  listBorder mb-2"
                  >
                    <VCheckbox
                      v-model="ap.selected"
                      class="me-3"
                      hide-details
                    />
                    <div class="ap-info flex-grow-1">
                      <div class="ap-name">
                        {{ ap.name }}
                        <VChip
                          class="ms-2 mb-2"
                          size="small"
                          color="info"
                        >
                          <span class="font-weight-medium">{{ ap.model }}</span>
                        </VChip>
                      </div>
                      <div class="ap-details text-caption text-disabled">
                        {{ ap.sn }}
                        <span class="ms-2 me-2">/</span>
                        {{ ap.mac }}
                      </div>
                    </div>
                    <VChip
                      :color="ap.online ? 'success' : 'error'"
                      size="small"
                      label
                    >
                      {{ ap.online ? '在线' : '离线' }}
                    </VChip>
                  </div>
                </div>
                <!-- AP分页 -->
                <div
                  v-if="apData.total > apItemsPerPage"
                  class="mt-4"
                >
                  <TablePagination
                    v-model:page="apPage"
                    :items-per-page="apItemsPerPage"
                    :total-items="apData.total"
                  />
                </div>
              </div>
            </div>
          </PerfectScrollbar>
        </div>

        <!-- 底部固定区域 -->
        <VDivider />
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-space-between">
          <div />
          <div>
            <VBtn
              class="me-4"
              color="secondary"
              variant="tonal"
              @click="closeEditDrawer"
            >
              取消
            </VBtn>
            <VBtn
              color="primary"
              @click="saveEditGroup"
            >
              创建
            </VBtn>
          </div>
        </div>
      </div>
    </VNavigationDrawer>

    <!-- 左侧AP列表查看抽屉 -->
    <VNavigationDrawer
      v-model="isApListDrawerOpen"
      location="end"
      temporary
      width="500"
      class="ap-list-drawer"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            分组详情
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="closeApListDrawer"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 分组名称 -->
        <div class="group-header pa-4">
          <div class="group-name text-h6">
            {{ currentViewGroup?.name }}
          </div>
        </div>

        <VDivider />

        <!-- 中间可滚动区域 -->
        <div class="flex-grow-1 overflow-y-scroll">
          <PerfectScrollbar
            :options="{ wheelPropagation: false }"
            class="h-100"
            tag="div"
          >
            <div class="ap-list-area">
              <div
                v-for="ap in currentGroupAps"
                :key="ap.id"
                class="ap-list-item d-flex align-center pa-4  listBorder mb-2"
              >
                <div class="ap-info flex-grow-1">
                  <div class="ap-name text-primary">
                    {{ ap.name }}
                    <VChip
                      class="ms-2 mb-2"
                      size="small"
                      color="info"
                    >
                      <span class="font-weight-medium">{{ ap.model }}</span>
                    </VChip>
                  </div>
                  <div class="ap-serial text-caption text-disabled">
                    {{ ap.sn }}
                    <span class="ms-2 me-2">/</span>
                    {{ ap.mac }}
                  </div>
                </div>
                <VBtn
                  variant="outlined"
                  @click="viewApDetail(ap)"
                >
                  设备详情
                </VBtn>
              </div>
            </div>
          </PerfectScrollbar>
        </div>
      </div>
    </VNavigationDrawer>

    <!-- 重启设备详情抽屉 -->
    <VNavigationDrawer
      v-model="isRestartDetailDrawerOpen"
      location="end"
      temporary
      width="600"
      class="operation-detail-drawer"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            重启设备详情
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="closeOperationDrawers"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 分组信息 -->
        <div class="group-header pa-4">
          <div class="group-name text-h6">
            {{ currentOperationGroup?.name }}
          </div>
          <div class="text-caption text-disabled">
            共 {{ currentOperationGroup?.apCount }} 个设备
          </div>
        </div>

        <VDivider />

        <!-- 设备列表 -->
        <div class="flex-grow-1 overflow-y-scroll">
          <PerfectScrollbar
            :options="{ wheelPropagation: false }"
            class="h-100"
            tag="div"
          >
            <div class="device-list-area">
              <div
                v-for="ap in resultList"
                :key="ap.id"
                class="device-list-item d-flex align-center pa-4"
              >
                <div class="device-info flex-grow-1">
                  <div class="device-name text-primary">
                    {{ ap.name }}
                    <VChip
                      class="ms-2 mb-2"
                      size="small"
                      color="info"
                    >
                      <span class="font-weight-medium">{{ ap.model }}</span>
                    </VChip>
                  </div>
                  <div class="device-serial text-caption text-disabled">
                    {{ ap.sn }}
                    <span class="ms-2 me-2">/</span>
                    {{ ap.mac }}
                  </div>
                </div>
                <div class="device-status d-flex flex-column align-end">
                  <VChip
                    v-if="ap.result !== undefined"
                    :color="ap.result ? 'success' : 'error'"
                    size="small"
                    label
                  >
                    {{ ap.result ? '设备已重启' : '重启失败，请检查' }}
                  </VChip>
                </div>
              </div>
            </div>
          </PerfectScrollbar>
        </div>
      </div>
    </VNavigationDrawer>

    <!-- 恢复出厂设置详情抽屉 -->
    <VNavigationDrawer
      v-model="isResetDetailDrawerOpen"
      location="end"
      temporary
      width="600"
      class="operation-detail-drawer"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            恢复出厂设置详情
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="closeOperationDrawers"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 分组信息 -->
        <div class="group-header pa-4">
          <div class="group-name text-h6">
            {{ currentOperationGroup?.name }}
          </div>
          <div class="text-caption text-disabled">
            共 {{ currentOperationGroup?.apCount }} 个设备
          </div>
        </div>

        <VDivider />

        <!-- 设备列表 -->
        <div class="flex-grow-1 overflow-y-scroll">
          <PerfectScrollbar
            :options="{ wheelPropagation: false }"
            class="h-100"
            tag="div"
          >
            <div class="device-list-area">
              <div
                v-for="ap in resultList"
                :key="ap.id"
                class="device-list-item d-flex align-center pa-4"
              >
                <div class="device-info flex-grow-1">
                  <div class="device-name text-primary">
                    {{ ap.name }}
                    <VChip
                      class="ms-2 mb-2"
                      size="small"
                      color="info"
                    >
                      <span class="font-weight-medium">{{ ap.model }}</span>
                    </VChip>
                  </div>
                  <div class="device-serial text-caption text-disabled">
                    {{ ap.sn }}
                    <span class="ms-2 me-2">/</span>
                    {{ ap.mac }}
                  </div>
                </div>
                <div class="device-status d-flex flex-column align-end">
                  <VChip
                    v-if="ap.result !== undefined"
                    :color="ap.result ? 'success' : 'error'"
                    size="small"
                    label
                  >
                    {{ ap.result ? '设备已重启' : '重启失败，请检查' }}
                  </VChip>
                </div>
              </div>
            </div>
          </PerfectScrollbar>
        </div>
      </div>
    </VNavigationDrawer>

    <!-- 检查并更新详情抽屉 -->
    <VNavigationDrawer
      v-model="isUpdateDetailDrawerOpen"
      location="end"
      temporary
      width="600"
      class="operation-detail-drawer"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            检查并更新详情
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="closeOperationDrawers"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 分组信息 -->
        <div class="group-header pa-4">
          <div class="group-name text-h6">
            {{ currentOperationGroup?.name }}
          </div>
          <div class="text-caption text-disabled">
            共 {{ currentOperationGroup?.apCount }} 个设备
          </div>
        </div>

        <VDivider />

        <!-- 设备列表 -->
        <div class="flex-grow-1 overflow-y-scroll">
          <PerfectScrollbar
            :options="{ wheelPropagation: false }"
            class="h-100"
            tag="div"
          >
            <div class="device-list-area">
              <div
                v-for="ap in resultList"
                :key="ap.id"
                class="device-list-item d-flex align-center pa-4"
              >
                <div class="device-info flex-grow-1">
                  <div class="device-name text-primary">
                    {{ ap.name }}
                    <VChip
                      class="ms-2 mb-2"
                      size="small"
                      color="info"
                    >
                      <span class="font-weight-medium">{{ ap.model }}</span>
                    </VChip>
                  </div>
                  <div class="device-serial text-caption text-disabled">
                    {{ ap.sn }}
                    <span class="ms-2 me-2">/</span>
                    {{ ap.mac }}
                  </div>
                </div>
                <div class="device-status d-flex flex-column align-end">
                  <VChip
                    v-if="ap.result !== undefined"
                    :color="ap.result ? 'success' : 'error'"
                    size="small"
                    label
                  >
                    {{ ap.result ? '设备已重启' : '重启失败，请检查' }}
                  </VChip>
                </div>
              </div>
            </div>
          </PerfectScrollbar>
        </div>
      </div>
    </VNavigationDrawer>
  </div>
</template>

<style scoped lang="scss">
.overflow-y-scroll {
  // 隐藏滚动条
  &::-webkit-scrollbar {
    display: none;
  }
}

.device-group-page {
  block-size: 100%;
}

.group-table-card {
  .table-pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;

    .pagination-controls {
      margin: 0;
    }
  }
}

// 抽屉样式 - 新建和编辑共用
.add-group-drawer,
.edit-group-drawer {
  // AP-style form classes
  .h-38 {
    block-size: 38px;
    line-height: 38px;
  }

  .w-80 {
    inline-size: 80px;
  }

  .section-label {
    color: rgba(var(--v-theme-on-surface), 0.87);
    font-size: 14px;
    font-weight: 500;
  }

  .ap-selection-area {
    border: 1px solid rgba(var(--v-theme-outline), 0.12);
    border-radius: 4px;

    .ap-item {
      border-block-end: 1px solid rgba(var(--v-theme-outline), 0.08);
      transition: background-color 0.2s ease;

      &:hover {
        background-color: rgba(var(--v-theme-primary), 0.04);
      }

      &:last-child {
        border-block-end: none;
      }

      .ap-info {
        .ap-name {
          font-size: 14px;
          font-weight: 500;
          line-height: 1.4;
        }

        .ap-details {
          font-size: 12px;
          margin-block-start: 2px;
        }
      }
    }

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      inline-size: 6px;
    }

    &::-webkit-scrollbar-track {
      border-radius: 3px;
      background: rgba(var(--v-theme-outline), 0.08);
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 3px;
      background: rgba(var(--v-theme-outline), 0.3);

      &:hover {
        background: rgba(var(--v-theme-outline), 0.5);
      }
    }
  }
}

// AP列表查看抽屉样式
.ap-list-drawer {
  .group-header {
    background-color: rgba(var(--v-theme-surface), 1);

    .group-name {
      color: rgba(var(--v-theme-on-surface), 0.87);
      font-weight: 500;
    }
  }

  .ap-list-area {
    .ap-list-item {
      border-block-end: 1px solid rgba(var(--v-theme-outline), 0.08);
      transition: background-color 0.2s ease;

      &:hover {
        background-color: rgba(var(--v-theme-primary), 0.04);
      }

      &:last-child {
        border-block-end: none;
      }

      .ap-info {
        .ap-name {
          color: rgb(var(--v-theme-primary));
          font-size: 14px;
          font-weight: 500;
          line-height: 1.4;
        }

        .ap-serial {
          color: rgba(var(--v-theme-on-surface), 0.6);
          font-size: 12px;
          margin-block-start: 4px;
        }
      }
    }
  }
}

// 操作详情抽屉样式
.operation-detail-drawer {
  .group-header {
    background-color: rgba(var(--v-theme-surface), 1);

    .group-name {
      color: rgba(var(--v-theme-on-surface), 0.87);
      font-weight: 500;
    }
  }

  .operation-status {
    background-color: rgba(var(--v-theme-surface-variant), 0.3);
  }

  .device-list-area {
    .device-list-item {
      border-block-end: 1px solid rgba(var(--v-theme-outline), 0.08);
      transition: background-color 0.2s ease;

      &:hover {
        background-color: rgba(var(--v-theme-primary), 0.04);
      }

      &:last-child {
        border-block-end: none;
      }

      .device-info {
        .device-name {
          color: rgb(var(--v-theme-primary));
          font-size: 14px;
          font-weight: 500;
          line-height: 1.4;
        }

        .device-serial {
          color: rgba(var(--v-theme-on-surface), 0.6);
          font-size: 12px;
          margin-block-start: 4px;
        }
      }

      .device-status {
        min-inline-size: 100px;
      }
    }
  }
}

.listBorder {
  border: 1px solid var(--Color-Secondary-secondary-100, #e6e6e9);
  border-radius: 6px;
  background: rgba(250, 250, 250, 100%);
}
</style>
